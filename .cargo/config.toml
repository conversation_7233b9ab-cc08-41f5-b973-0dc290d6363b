# AvoRed Rust CMS - Cargo Build Optimization Configuration
# This file optimizes build performance for faster development cycles

[build]
# Use all available CPU cores for parallel compilation
jobs = 8

# Enable faster incremental compilation
incremental = true

# Use faster linker (mold/lld) when available
rustflags = [
    "-C", "link-arg=-fuse-ld=lld",  # Use LLD linker for faster linking
    "-C", "target-cpu=native",       # Optimize for current CPU
]

# Platform-specific optimizations for macOS
[target.x86_64-apple-darwin]
rustflags = [
    "-C", "link-arg=-fuse-ld=lld",
    "-C", "target-cpu=native",
]

[target.aarch64-apple-darwin]
rustflags = [
    "-C", "link-arg=-fuse-ld=lld", 
    "-C", "target-cpu=native",
]

# Development profile optimizations
[profile.dev]
# Minimal debug info for faster builds
debug = 1
# No optimization for fastest compilation
opt-level = 0
# Enable incremental compilation
incremental = true
# Faster builds with less precise debugging
debug-assertions = true
# Reduce binary size
lto = false
# Faster compilation
codegen-units = 256
# Reduce memory usage during compilation
split-debuginfo = "unpacked"

# Fast development profile for when you need some optimization
[profile.dev-fast]
inherits = "dev"
opt-level = 1
debug = false
incremental = true
codegen-units = 16

# Optimized development profile for testing performance
[profile.dev-opt]
inherits = "dev"
opt-level = 2
debug = 1
incremental = true
lto = "thin"

# Release profile optimizations
[profile.release]
opt-level = 3
debug = false
lto = "fat"
codegen-units = 1
panic = "abort"
strip = true

# Dependency-specific optimizations
# Compile dependencies with some optimization even in dev mode
[profile.dev.package."*"]
opt-level = 1
debug = false

# Heavy dependencies that benefit from optimization
[profile.dev.package.surrealdb]
opt-level = 2

[profile.dev.package.tonic]
opt-level = 1

[profile.dev.package.prost]
opt-level = 1

[profile.dev.package.tokio]
opt-level = 1

[profile.dev.package.axum]
opt-level = 1

# Registry configuration for faster downloads
# Use sparse registry for faster index updates
[registries.crates-io]
protocol = "sparse"

# Build cache configuration (uncomment when sccache is installed)
# [env]
# RUSTC_WRAPPER = "sccache"
# SCCACHE_CACHE_SIZE = "10G"
# SCCACHE_DIR = ".sccache"

# Compilation flags for better error messages and faster builds
[unstable]
# Enable unstable features for better build performance
build-std = false
# Use newer resolver for better dependency resolution
resolver = "2"
