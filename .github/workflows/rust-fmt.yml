name: Rust fmt check

on: [push, pull_request]

env:
  CARGO_TERM_COLOR: always

jobs:
  test:
    name: AvoredCMS
    runs-on: ubuntu-22.04
    
    steps:
      - uses: actions/checkout@v4
      - uses: Swatinem/rust-cache@v2.4.0
      - uses: arduino/setup-protoc@v3
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
      - uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: stable
          override: true
      - name: Rust Check
        run: |
          cargo check
