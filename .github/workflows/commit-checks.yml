name: Docker Image CI

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

jobs:
#  build:
#    runs-on: ubuntu-latest
#    steps:
#    - uses: actions/checkout@v4
#    - uses: Swatinem/rust-cache@v2.4.0
#    - name: Build the Docker image
#      run: docker build . --file Dockerfile --tag marks:$(date +%s)
#  format:
#    runs-on: ubuntu-latest
#    steps:
#      - uses: actions/checkout@v3
#      - uses: dtolnay/rust-toolchain@stable
#      - uses: Swatinem/rust-cache@v2.4.0
#      - uses: mbrobbel/rustfmt-check@master
#        with:
#          token: ${{ secrets.CI_TOKEN }}

#          mode: review ## Review mode requires to be on pull_request only..
#    steps:
#    - uses: actions/checkout@v3
#      with:
#        repository: avored/avored-rust-cms
#    - uses: Swatinem/rust-cache@v2.4.0
#    - name: github-action-auto-format
#      uses: cloudposse/github-action-auto-format@v0.12.0
#      with:
#        # GitHub Token for use `github_format.sh` and PR creation steps. This token must be granted `workflows` permissions.
#        workflow-token: ${{ secrets.CI_TOKEN }}
  greet:
    runs-on: ubuntu-latest
    steps:
    - name: First interaction
      uses: actions/first-interaction@v1.1.1
      with:
        # Token for the repository. Can be passed in using {{ secrets.GITHUB_TOKEN }}
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        issue-message: |
          Hello! Thank you for filing an issue.
          
          Please include relevant logs or detailed description for faster resolutions.
          
          We really appreciate your contribution! 
          
          Thanks 
          Avored
        pr-message: |
          Hello! Thank you for your contribution.
          
          If you are fixing a bug, please reference the issue number in the description.
          
          If you are implementing a feature request, please check with the maintainers that the feature will be accepted first.
          
          We really appreciate your contribution!
          
          Thanks 
          Avored 
